<!doctype html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>

    <title>[[#{page.user.my.account}]]</title>

    <style>
        .htmx-settling img {
            opacity: 0;
        }

        img {
            transition: opacity 300ms ease-in;
        }
    </style>
</head>
<body>

<th:block layout:fragment="optionalPageCSS">
    <!-- Page CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/select2/select2.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/@form-validation/form-validation.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/animate-css/animate.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/sweetalert2/sweetalert2.css}"/>

    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/flatpickr/flatpickr.css}"/>
</th:block>

<div layout:fragment="content">

    <div class="row">
        <div class="col-md-12">
            <div class="nav-align-top">
                <ul class="nav nav-pills flex-column flex-md-row mb-6 gap-2 gap-lg-0">
                    <li class="nav-item"><a class="nav-link active" href="javascript:void(0);"><i
                            class="ti-sm ti ti-users me-1_5"></i> [[#{page.user.my.account}]]</a></li>
                    <li class="nav-item"><a class="nav-link" th:href="@{/user/securityuser}"><i
                            class="ti-sm ti ti-lock me-1_5"></i> [[#{page.user.security}]]</a></li>
                </ul>
            </div>
            <div class="card mb-6">
                <!-- Account -->
                <form id="formAccountSettings"
                      hx-post="/users/updateuser"
                      hx-swap="innerHTML"
                      hx-target="#response"
                      hx-on::after-request="this.classList.remove('was-validated'), ResultModalMsg()"
                      hx-on:htmx:response-error="ModalError()"
                      hx-trigger="bs-send"
                      enctype="multipart/form-data"
                      class="row g-2 needs-validation"
                      novalidate>
                    <div class="card-body">
                        <div class="d-flex align-items-start align-items-sm-center gap-6">
                            <img alt="user-avatar"
                                 th:src="${currentUser.getPhoto()} ? ${'/images/' + currentUser.getPhoto()} :  '../../assets/img/avatars/silhouette.png'"
                                 class="d-block w-px-100 h-px-100 rounded" id="uploadedAvatar"/>

                            <div class="button-wrapper">
                                <label for="upload" class="btn btn-primary me-3 mb-4" tabindex="0">
                                    <span class="d-none d-sm-block">Nova foto</span>
                                    <i class="ti ti-upload d-block d-sm-none"></i>
                                    <input type="file" name="file" id="upload" class="account-file-input" hidden
                                           accept="image/png, image/jpeg"/>
                                </label>
                                <button type="button" class="btn btn-label-secondary account-image-reset me-3 mb-4">
                                    <i class="ti ti-refresh-dot d-block d-sm-none"></i>
                                    <span class="d-none d-sm-block">Reset</span>
                                </button>
                                <div>Arquivos permitidos JPG, GIF ou PNG. Máx de 800K</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body pt-4">

                        <!--hx-on::after-request="this.classList.remove('was-validated')"-->

                        <div class="row">
                            <div class="mb-4 col-md-6">
                                <label for="fullName" class="form-label">[[#{user.field.fullName}]]</label>
                                <input class="form-control" type="text" id="fullName" name="fullName" required
                                       th:value="${currentUser.getFullName()}" autofocus/>
                                <div class="invalid-feedback">
                                    [[#{user.validation.fullName.required}]]
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="company" class="form-label">Empresa</label>
                                <input type="text" class="form-control" id="company" required
                                       th:value="${currentUser.getCompany()}"
                                       placeholder="Live Cenografia" readonly="readonly"/>
                                <div class="invalid-feedback">
                                    Por favor digite o nome da empresa que está atuando.
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="email" class="form-label">E-mail</label>
                                <input class="form-control" type="text" id="email" required
                                       th:value="${currentUser.getEmail()}"
                                       placeholder="<EMAIL>" readonly="readonly"/>
                                <div class="invalid-feedback">
                                    Email de cadastro obrigatório.
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label" for="phoneNumber">Celular</label>
                                <div class="input-group input-group-merge">
                                    <span class="input-group-text">BR (+55)</span>
                                    <input type="text" id="phoneNumber" name="phone" class="form-control"
                                           th:value="${currentUser.getPhone()}"
                                           placeholder="(11) 90000-0000"/>
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label" for="country">País</label>
                                <select id="country" class="select2 form-select" name="country" required>
                                    <option value="">Escolha o país</option>
                                    <option th:each="l : ${allCountries}"
                                            th:value="${l.code}"
                                            th:text="${l.name}"
                                            th:selected="(${l.code} == ${currentUser.getCountry()})">
                                    </option>
                                </select>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="language" class="form-label">Língua</label>
                                <select id="language" class="select2 form-select" name="language">
                                    <option value="">Escolha a Língua</option>
                                    <option th:each="l : ${allLanguages}"
                                            th:value="${l.code}"
                                            th:text="${l.name}"
                                            th:selected="(${l.code} == ${currentUser.getLanguage()})">
                                    </option>
                                </select>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="city" class="form-label">Cidade</label>
                                <input type="text" class="form-control" id="city" name="city"
                                       th:value="${currentUser.getCity()}"
                                       placeholder="São Paulo"/>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="workFormat" class="form-label">Formato de Trabalho</label>
                                <select id="workFormat" class="select2 form-select" name="workFormat">
                                    <option value="">Escolha o formato</option>
                                    <option th:each="wf : ${allWorkFormats}"
                                            th:value="${wf.id}"
                                            th:text="${wf.name}"
                                            th:selected="(${wf.id} == ${currentUser.getWork_format()})">
                                    </option>
                                </select>

                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="position" class="form-label">Função</label>
                                <input type="text" class="form-control" id="position" name="position"
                                       th:value="${currentUser.getPosition()}"
                                       placeholder="Produtor"/>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label for="joinedDate" class="form-label">Data de Início</label>
                                <input type="text" class="form-control flatpickr-input active date-picker"
                                       th:value="${currentUser.getJoined_date()}"
                                       placeholder="YYYY-MM-DD" id="joinedDate" name="joinedDate">
                            </div>
                        </div>
                        <div class="mt-2">
                            <button class="btn btn-primary"
                                    type="submit"
                                    hx-indicator="#spinner"
                                    id="save-user-changes">
                                Salvar alterações
                            </button>
                            <img id="spinner" class="htmx-indicator mx-1" src="/img/bars.svg"/>

                            <button type="reset" class="btn btn-label-secondary">Cancelar</button>
                        </div>

                    </div>
                </form>

                <div id="response" class="text-bg-danger mt-5" hidden>
                    <span id="responseMsg" th:text="${response}"></span>
                </div>
                <!-- /Account -->
            </div>
        </div>
    </div>

</div>

<th:block layout:fragment="optionalVendorJS">
    <script th:src="@{/assets/vendor/libs/select2/select2.js}"></script>
    <script th:src="@{/assets/vendor/libs/@form-validation/popular.js}"></script>
    <script th:src="@{/assets/vendor/libs/@form-validation/bootstrap5.js}"></script>
    <script th:src="@{/assets/vendor/libs/@form-validation/auto-focus.js}"></script>
    <script th:src="@{/assets/vendor/libs/cleavejs/cleave.js}"></script>
    <script th:src="@{/assets/vendor/libs/cleavejs/cleave-phone.js}"></script>
    <script th:src="@{/assets/vendor/libs/sweetalert2/sweetalert2.js}"></script>

    <script th:src="@{/assets/vendor/libs/flatpickr/flatpickr.js}"></script>
    <script th:src="@{/assets/vendor/libs/flatpickr/pt.js}"></script>
</th:block>

<th:block layout:fragment="optionalPageJS">
    <script th:src="@{/assets/js/pages-account-settings-account.js}"></script>
    <script th:src="@{/js/pages/edituser-date-pickers.js}"></script>

    <script th:src="@{/js/htmx-extensions/bsSend.js}"></script>
    <script th:src="@{/js/modalResponse.js}"></script>

    <script>
        function ResultUploadAvatar() {
            var uploadResponseMsg = document.querySelector("#uploadResponse").innerHTML;
            var uploadResponseObj = JSON.parse(uploadResponseMsg);

            console.log(uploadResponseObj.title);
        }

    </script>
</th:block>

</body>
</html>
